import { Wrap<PERSON>utton } from "@/components/ui/wrap-button";
import { MinimalCard, MinimalCardContent, MinimalCardTitle, MinimalCardDescription } from "@/components/ui/minimal-card";
import { Check, Star } from "lucide-react";

const PricingSection = () => {
  const plans = [
    {
      name: "Basic",
      price: "₹999",
      period: "/month",
      description: "Perfect for individual agents starting their digital journey",
      features: [
        "Up to 100 clients",
        "Basic dashboard",
        "Investment tracking",
        "Email notifications",
        "Standard reports",
        "Mobile app access"
      ],
      popular: false,
      cta: "Start Basic Plan"
    },
    {
      name: "Pro",
      price: "₹1,999",
      period: "/month",
      description: "Most popular choice for growing Post Office agencies",
      features: [
        "Up to 500 clients",
        "Advanced dashboard",
        "Full investment management",
        "SMS & Email alerts",
        "Custom reports",
        "Commission tracking",
        "Family management",
        "Priority support"
      ],
      popular: true,
      cta: "Choose Pro Plan"
    },
    {
      name: "Business",
      price: "₹3,999",
      period: "/month",
      description: "Enterprise solution for large agencies and teams",
      features: [
        "Unlimited clients",
        "Complete feature access",
        "Multi-user support",
        "Role-based permissions",
        "API access",
        "Custom integrations",
        "Dedicated support",
        "Training included",
        "White-label options"
      ],
      popular: false,
      cta: "Go Business"
    }
  ];

  return (
    <section id="pricing" className="py-20 bg-background-secondary">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Simple, Transparent{" "}
            <span className="bg-gradient-to-r from-[#113F67] to-[#40aca3] bg-clip-text text-transparent">
              Pricing
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Choose the plan that fits your agency size. All plans include core features with no hidden costs.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <MinimalCard 
              key={index} 
              className={`relative group hover:scale-[1.02] hover:shadow-xl transition-all duration-500 ${
                plan.popular 
                  ? 'bg-gradient-to-br from-[#113F67] to-[#40aca3] text-white border-2 border-[#113F67]' 
                  : 'bg-white border border-gray-100'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-gray-900 px-4 py-2 rounded-full text-sm font-semibold flex items-center gap-2 shadow-lg">
                    <Star className="w-4 h-4" />
                    Most Popular
                  </div>
                </div>
              )}

              <MinimalCardContent className="p-8 space-y-6">
                <div className="text-center space-y-4">
                  <MinimalCardTitle className={`text-2xl font-bold ${plan.popular ? 'text-white' : 'text-gray-900'}`}>
                    {plan.name}
                  </MinimalCardTitle>
                  <div className="space-y-2">
                    <div className="flex items-baseline justify-center gap-1">
                      <span className={`text-4xl font-bold ${plan.popular ? 'text-white' : 'text-gray-900'}`}>
                        {plan.price}
                      </span>
                      <span className={`text-lg ${plan.popular ? 'text-white/80' : 'text-gray-600'}`}>
                        {plan.period}
                      </span>
                    </div>
                    <MinimalCardDescription className={`text-sm ${plan.popular ? 'text-white/80' : 'text-gray-600'}`}>
                      {plan.description}
                    </MinimalCardDescription>
                  </div>
                </div>

                <ul className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center gap-3">
                      <div className={`w-5 h-5 rounded-full flex items-center justify-center ${
                        plan.popular ? 'bg-white/20' : 'bg-[#40aca3]/20'
                      }`}>
                        <Check className={`w-3 h-3 ${
                          plan.popular ? 'text-white' : 'text-[#40aca3]'
                        }`} />
                      </div>
                      <span className={`text-sm ${
                        plan.popular ? 'text-white/90' : 'text-gray-600'
                      }`}>
                        {feature}
                      </span>
                    </li>
                  ))}
                </ul>

                <div className="w-full flex justify-center">
                  <WrapButton>
                    {plan.cta}
                  </WrapButton>
                </div>
              </MinimalCardContent>
            </MinimalCard>
          ))}
        </div>

        <div className="text-center mt-12">
          <MinimalCard className="inline-block bg-gradient-to-r from-[#113F67]/5 to-[#40aca3]/5 border border-gray-100 px-8 py-6">
            <p className="text-gray-600 mb-4">
              All plans include 7-day free trial • No setup fees • Cancel anytime
            </p>
            <div className="flex items-center justify-center gap-6 text-sm text-gray-600">
              <span>✓ 99.9% Uptime SLA</span>
              <span>✓ 24/7 Support</span>
              <span>✓ Data Security</span>
            </div>
          </MinimalCard>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;