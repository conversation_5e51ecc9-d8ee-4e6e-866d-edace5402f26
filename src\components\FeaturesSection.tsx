import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { MinimalCard, MinimalCardContent, MinimalCardTitle, MinimalCardDescription } from "@/components/ui/minimal-card";
import { 
  LayoutDashboard, 
  Users, 
  TrendingUp, 
  Settings, 
  Bell, 
  FileText, 
  Calculator,
  Shield
} from "lucide-react";

const FeaturesSection = () => {
  const features = [
    {
      id: "dashboard",
      title: "Smart Dashboard",
      description: "Get a complete overview of your business at a glance with real-time analytics and insights.",
      icon: LayoutDashboard,
      details: [
        "Real-time business metrics",
        "Visual charts and graphs",
        "Quick access to key functions",
        "Customizable widgets"
      ]
    },
    {
      id: "clients",
      title: "Client & Family Management",
      description: "Easily manage client and their family data with comprehensive profiles and relationship tracking.",
      icon: Users,
      details: [
        "Complete client profiles",
        "Family member tracking",
        "Contact management",
        "Document storage"
      ]
    },
    {
      id: "investments",
      title: "Investment Management",
      description: "Add new investments and handle maturities with easy reinvest or transfer options.",
      icon: TrendingUp,
      details: [
        "Investment portfolio tracking",
        "Maturity date management",
        "Automated reinvestment",
        "Transfer processing"
      ]
    },
    {
      id: "schemes",
      title: "Schemes & Commissions",
      description: "Define all your investment schemes and commission structures with flexible rules.",
      icon: Settings,
      details: [
        "Scheme configuration",
        "Commission calculations",
        "Rate management",
        "Bonus tracking"
      ]
    },
    {
      id: "alerts",
      title: "Automated Alerts",
      description: "Send automatic reminders to clients via SMS and Email for important dates and updates.",
      icon: Bell,
      details: [
        "Maturity date alerts",
        "SMS notifications",
        "Email reminders",
        "Custom messaging"
      ]
    },
    {
      id: "reports",
      title: "Powerful Reporting",
      description: "Generate PDF or Excel reports on any data instantly with professional formatting.",
      icon: FileText,
      details: [
        "PDF report generation",
        "Excel exports",
        "Custom report templates",
        "Scheduled reporting"
      ]
    },
    {
      id: "calculator",
      title: "Easy Calculator",
      description: "Show clients potential returns with instant calculations and scenario planning.",
      icon: Calculator,
      details: [
        "Return calculations",
        "Interest projections",
        "Scenario planning",
        "Visual presentations"
      ]
    },
    {
      id: "permissions",
      title: "Settings & Permissions",
      description: "Set roles and permissions for your staff with granular access control.",
      icon: Shield,
      details: [
        "User role management",
        "Access permissions",
        "Data security",
        "Audit trails"
      ]
    }
  ];

  const [activeFeature, setActiveFeature] = useState(features[0]);

  return (
    <section id="features" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Powerful Features Built for{" "}
            <span className="bg-gradient-to-r from-[#113F67] to-[#40aca3] bg-clip-text text-transparent">
              Your Success
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Everything you need to manage your Post Office agency business efficiently and professionally.
          </p>
        </div>

        <div className="max-w-7xl mx-auto">
          {/* Interactive Feature Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              const isActive = activeFeature.id === feature.id;
              return (
                <MinimalCard
                  key={feature.id}
                  className={`group cursor-pointer transition-all duration-500 ${
                    isActive 
                      ? 'scale-105 shadow-xl border-2 border-[#113F67]' 
                      : 'hover:scale-[1.02] hover:shadow-lg border border-gray-100'
                  }`}
                  onClick={() => setActiveFeature(feature)}
                >
                  <MinimalCardContent className="p-6 space-y-4 h-full flex flex-col">
                    <div className="relative">
                      <div className={`w-14 h-14 rounded-xl flex items-center justify-center transition-all duration-300 ${
                        isActive 
                          ? 'bg-gradient-to-br from-[#113F67] to-[#40aca3] shadow-lg' 
                          : 'bg-gradient-to-br from-[#113F67]/10 to-[#40aca3]/10 group-hover:from-[#113F67]/20 group-hover:to-[#40aca3]/20'
                      }`}>
                        <Icon className={`w-7 h-7 ${isActive ? 'text-white' : 'text-[#113F67]'}`} />
                      </div>
                      {isActive && (
                        <div className="absolute -inset-2 bg-gradient-to-br from-[#113F67]/20 to-[#40aca3]/20 rounded-2xl blur-lg opacity-50"></div>
                      )}
                    </div>
                    
                    <MinimalCardTitle className={`font-bold text-lg transition-colors ${
                      isActive ? 'text-[#113F67]' : 'text-gray-900'
                    }`}>
                      {feature.title}
                    </MinimalCardTitle>
                    
                    <MinimalCardDescription className="text-sm text-gray-600 leading-relaxed">
                      {feature.description}
                    </MinimalCardDescription>

                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <div className={`w-2 h-2 rounded-full transition-colors ${
                        isActive ? 'bg-[#40aca3]' : 'bg-gray-400'
                      }`}></div>
                      {feature.details.length} features
                    </div>
                  </MinimalCardContent>
                </MinimalCard>
              );
            })}
          </div>

          {/* Active Feature Detail */}
          <MinimalCard className="bg-white border border-gray-100 shadow-xl">
            <MinimalCardContent className="p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div className="space-y-8">
                  <div className="flex items-center gap-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-[#113F67] to-[#40aca3] rounded-2xl flex items-center justify-center shadow-lg">
                      <activeFeature.icon className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-3xl font-bold text-gray-900 mb-2">{activeFeature.title}</h3>
                      <div className="w-16 h-1 bg-gradient-to-r from-[#113F67] to-[#40aca3] rounded-full"></div>
                    </div>
                  </div>
                  
                  <p className="text-xl text-gray-600 leading-relaxed">
                    {activeFeature.description}
                  </p>

                  <div className="space-y-6">
                    <h4 className="text-lg font-bold text-gray-900 flex items-center gap-2">
                      <div className="w-2 h-2 bg-[#40aca3] rounded-full"></div>
                      Key Capabilities
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {activeFeature.details.map((detail, index) => (
                        <MinimalCard key={index} className="bg-gray-50 border border-gray-100">
                          <div className="flex items-center gap-3 p-3">
                            <div className="w-6 h-6 bg-gradient-to-br from-[#113F67]/10 to-[#40aca3]/10 rounded-lg flex items-center justify-center flex-shrink-0">
                              <div className="w-2 h-2 bg-[#40aca3] rounded-full"></div>
                            </div>
                            <span className="text-gray-700">{detail}</span>
                          </div>
                        </MinimalCard>
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <Button className="bg-gradient-to-r from-[#113F67] to-[#40aca3] hover:from-[#0d2f4f] hover:to-[#369189] text-white px-6 py-3 rounded-xl font-semibold">
                      Try This Feature
                    </Button>
                    <Button className="bg-white border border-[#113F67] text-[#113F67] hover:bg-[#113F67] hover:text-white px-6 py-3 rounded-xl font-semibold transition-all">
                      View Demo
                    </Button>
                  </div>
                </div>

                <div className="relative">
                  <MinimalCard className="bg-gradient-to-br from-[#113F67]/5 to-[#40aca3]/5 border border-gray-100">
                    <div className="p-8">
                      <div className="aspect-[4/3] bg-gradient-to-br from-[#113F67]/10 via-transparent to-[#40aca3]/10 rounded-xl flex items-center justify-center relative overflow-hidden">
                        <activeFeature.icon className="w-24 h-24 text-[#113F67]/30" />
                        
                        {/* Decorative elements */}
                        <div className="absolute top-4 right-4 w-8 h-8 bg-[#113F67]/10 rounded-full"></div>
                        <div className="absolute bottom-6 left-6 w-4 h-4 bg-[#40aca3]/20 rounded-full"></div>
                        <div className="absolute top-1/2 left-4 w-2 h-2 bg-[#113F67]/30 rounded-full"></div>
                      </div>
                    </div>
                  </MinimalCard>
                </div>
              </div>
            </MinimalCardContent>
          </MinimalCard>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;