import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { WrapButton } from "@/components/ui/wrap-button";
import { Menu, X } from "lucide-react";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false);
  };

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 w-[100%] sm:w-[80%] mx-auto rounded-b-3xl sm:rounded-b-full backdrop-blur-md transition-all duration-300 ${
        isScrolled ? 'border-b border-border shadow-lg' : 'border-transparent shadow-none'
      }`}>
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16 w-98">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            {/* <div className="w-8 h-8 bg-gradient-to-r from-primary to-success rounded-lg flex items-center justify-center"> */}
            <img src="./l1.png" alt="" className=" h-12" />
            <img src="./l2.png" alt="" className=" h-8" />


          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <button
              onClick={() => scrollToSection('hero')}
              className="text-gray-600 hover:text-[#113F67] transition-colors"
            >
              Home
            </button>
            <button
              onClick={() => scrollToSection('features')}
              className="text-gray-600 hover:text-[#113F67] transition-colors"
            >
              Features
            </button>
            <button
              onClick={() => scrollToSection('pricing')}
              className="text-gray-600 hover:text-[#113F67] transition-colors"
            >
              Pricing
            </button>
            <button
              onClick={() => scrollToSection('testimonials')}
              className="text-gray-600 hover:text-[#113F67] transition-colors"
            >
              Testimonials
            </button>
            <button
              onClick={() => scrollToSection('faq')}
              className="text-gray-600 hover:text-[#113F67] transition-colors"
            >
              FAQ
            </button>
          </div>

          {/* CTA Button */}
          <div className="hidden md:block">
            <WrapButton onClick={() => scrollToSection('pricing')}>
              Request Demo
            </WrapButton>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 rounded-lg hover:bg-background-secondary"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-border">
            <div className="flex flex-col space-y-4">
              <button
                onClick={() => scrollToSection('hero')}
                className="text-left text-gray-600 hover:text-[#113F67] transition-colors py-2"
              >
                Home
              </button>
              <button
                onClick={() => scrollToSection('features')}
                className="text-left text-gray-600 hover:text-[#113F67] transition-colors py-2"
              >
                Features
              </button>
              <button
                onClick={() => scrollToSection('pricing')}
                className="text-left text-gray-600 hover:text-[#113F67] transition-colors py-2"
              >
                Pricing
              </button>
              <button
                onClick={() => scrollToSection('testimonials')}
                className="text-left text-gray-600 hover:text-[#113F67] transition-colors py-2"
              >
                Testimonials
              </button>
              <button
                onClick={() => scrollToSection('faq')}
                className="text-left text-gray-600 hover:text-[#113F67] transition-colors py-2"
              >
                FAQ
              </button>
              <WrapButton onClick={() => scrollToSection('pricing')}>
                Request Demo
              </WrapButton>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;