import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Play, Sparkles, Users, TrendingUp, Shield } from "lucide-react";
import { CardSwipe } from "@/components/ui/CardSwipe";
import { FlipText } from "@/components/ui/text-effect-flipper";

const HeroSection = () => {
  const scrollToFeatures = () => {
    document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' });
  };

  const scrollToDemo = () => {
    document.getElementById('pricing')?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section id="hero" className="relative min-h-screen flex items-center justify-center overflow-hidden pt-16">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#113F67]/10 via-white to-[#40aca3]/10">
        <div className="absolute inset-0 bg-gradient-to-r from-[#113F67]/5 to-[#40aca3]/5"></div>
        <div className="absolute top-0 left-0 w-full h-full opacity-30">
          <div className="absolute top-20 left-20 w-72 h-72 bg-[#113F67] rounded-full mix-blend-multiply filter blur-xl animate-float"></div>
          <div className="absolute top-40 right-20 w-72 h-72 bg-[#40aca3] rounded-full mix-blend-multiply filter blur-xl animate-float" style={{ animationDelay: '2s' }}></div>
          <div className="absolute bottom-20 left-40 w-72 h-72 bg-[#40aca3] rounded-full mix-blend-multiply filter blur-xl animate-float" style={{ animationDelay: '4s' }}></div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-20 relative z-10">
        <div className="grid lg:grid-cols-2 gap-16 items-center max-w-7xl mx-auto">
          {/* Left Content */}
          <div className="text-left space-y-8">
            <div className="space-y-6">
              <div className="inline-flex items-center gap-3 bg-gradient-to-r from-[#113F67]/10 to-[#40aca3]/10 backdrop-blur-sm border border-[#113F67]/30 px-6 py-3 rounded-full text-sm font-medium shadow-lg animate-shimmer">
                <div className="w-2 h-2 bg-[#40aca3] rounded-full animate-pulse"></div>
                <span className="bg-gradient-to-r from-[#113F67] to-[#40aca3] bg-clip-text text-transparent font-semibold">Trusted by 1000+ Post Office Agents</span>
              </div>

              <h1 className="text-5xl md:text-6xl lg:text-5xl font-bold leading-tight">
                <span className=" text-gray-900">The Most </span>
                <FlipText className="inline-block bg-gradient-to-r from-[#113F67] to-[#40aca3] bg-clip-text text-[#113F67] text-5xl md:text-6xl lg:text-5xl font-bold">Powerful</FlipText>
                <span className="block text-gray-900">Management Software</span>
                <span className="block text-gray-600">for Post Office Agents</span>
              </h1>

              <p className="text-xl text-gray-600 leading-relaxed max-w-2xl">
                Save Time, Delight Your Clients, and Grow Your Business with Ease.
                Manage everything from client data to commissions in one intelligent platform.
              </p>
            </div>

            {/* Feature Pills */}
            <div className="flex flex-wrap gap-3">
              <div className="flex items-center gap-2 bg-white/60 backdrop-blur-sm px-4 py-2 rounded-full border border-[#113F67]/30 shadow-sm hover:shadow-md transition-shadow">
                <Sparkles className="w-4 h-4 text-[#113F67]" />
                <FlipText className="text-sm font-medium text-[#113F67]">AI-Powered</FlipText>
              </div>
              <div className="flex items-center gap-2 bg-white/60 backdrop-blur-sm px-4 py-2 rounded-full border border-[#40aca3]/30 shadow-sm hover:shadow-md transition-shadow">
                <Shield className="w-4 h-4 text-[#40aca3]" />
                <FlipText className="text-sm font-medium text-[#40aca3]">Secure</FlipText>
              </div>
              <div className="flex items-center gap-2 bg-white/60 backdrop-blur-sm px-4 py-2 rounded-full border border-[#40aca3]/30 shadow-sm hover:shadow-md transition-shadow">
                <TrendingUp className="w-4 h-4 text-[#40aca3]" />
                <FlipText className="text-sm font-medium text-[#40aca3]">Growth Focused</FlipText>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={scrollToDemo}
                className="group bg-gradient-to-r from-[#113F67] to-[#40aca3] hover:from-[#0d2f4f] hover:to-[#369189] text-white px-8 py-4 rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300"
              >
                Request a Free Demo
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Button>

              <Button
                onClick={scrollToFeatures}
                className="group bg-white/80 backdrop-blur-sm hover:bg-white text-gray-700 hover:text-[#113F67] border border-gray-200 hover:border-[#113F67] px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300"
              >
                <Play className="mr-2 h-5 w-5" />
                View Features
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="grid grid-cols-3 gap-8 pt-8">
              <div className="text-center group">
                <div className="text-3xl font-bold bg-gradient-to-r from-[#113F67] to-[#40aca3] bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300">1000+</div>
                <div className="text-sm text-gray-600 font-medium">Active Agents</div>
              </div>
              <div className="text-center group">
                <div className="text-3xl font-bold bg-gradient-to-r from-[#40aca3] to-[#113F67] bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300">₹50Cr+</div>
                <div className="text-sm text-gray-600 font-medium">Investments Managed</div>
              </div>
              <div className="text-center group">
                <div className="text-3xl font-bold bg-gradient-to-r from-[#113F67] to-[#40aca3] bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300">99.9%</div>
                <div className="text-sm text-gray-600 font-medium">Uptime</div>
              </div>
            </div>
          </div>

          {/* Right Content - Card Swiper */}
          <div className="relative">
            <CardSwipe
              images={[
                { src: "/2.png", alt: "Dashboard View 1" },
                { src: "/3.png", alt: "Dashboard View 2" },
                { src: "/4.png", alt: "Dashboard View 3" },
                { src: "/5.png", alt: "Dashboard View 4" },
                { src: "/hero-dashboard.jpg", alt: "Main Dashboard" }
              ]}
              autoplayDelay={3000}
              slideShadows={true}
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;