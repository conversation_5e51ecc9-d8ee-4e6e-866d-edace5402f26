import { <PERSON>mal<PERSON>ard, MinimalCardContent, MinimalCardTitle, MinimalCardDescription } from "@/components/ui/minimal-card";
import { FileText, Calendar, Calculator, BarChart3 } from "lucide-react";

const ProblemsSection = () => {
  const problems = [
    {
      icon: FileText,
      problem: "Stuck in paperwork?",
      solution: "All client data in one place",
      description: "Eliminate manual paperwork and manage all client information digitally with our intuitive interface."
    },
    {
      icon: Calendar,
      problem: "Trouble remembering maturity dates?",
      solution: "Automated maturity alerts",
      description: "Never miss important dates with our smart notification system that keeps you and your clients informed."
    },
    {
      icon: Calculator,
      problem: "Complex commission calculations?",
      solution: "Instant and accurate commission reports",
      description: "Calculate commissions automatically with predefined rules and generate reports in seconds."
    },
    {
      icon: BarChart3,
      problem: "Hours spent creating reports?",
      solution: "Professional reports in one click",
      description: "Generate comprehensive PDF and Excel reports instantly with beautiful, professional formatting."
    }
  ];

  return (
    <section className="py-20 bg-background-secondary">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            Say Goodbye to Your Daily Struggles
          </h2>
          <p className="text-xl text-foreground-muted max-w-3xl mx-auto">
            We understand the challenges you face as a Post Office agent. InvestPro is designed to solve your biggest pain points.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
          {problems.map((item, index) => {
            const Icon = item.icon;
            return (
              <MinimalCard key={index} className="group hover:scale-[1.02] hover:shadow-xl transition-all duration-500 bg-white border border-gray-100">
                <MinimalCardContent className="text-center space-y-6 p-8">
                  <div className="relative">
                    <div className="w-20 h-20 bg-gradient-to-br from-[#113F67] to-[#40aca3] rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:shadow-xl transition-all duration-300">
                      <Icon className="w-10 h-10 text-white" />
                    </div>
                    <div className="absolute -inset-2 bg-gradient-to-br from-[#113F67]/20 to-[#40aca3]/20 rounded-3xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                  
                  <div className="space-y-3">
                    <MinimalCardTitle className="text-lg font-medium text-gray-500 leading-snug">
                      {item.problem}
                    </MinimalCardTitle>
                    <h4 className="text-xl font-bold bg-gradient-to-r from-[#113F67] to-[#40aca3] bg-clip-text text-transparent leading-tight">
                      {item.solution}
                    </h4>
                    <MinimalCardDescription className="text-sm text-gray-600 leading-relaxed px-2">
                      {item.description}
                    </MinimalCardDescription>
                  </div>
                </MinimalCardContent>
              </MinimalCard>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default ProblemsSection;