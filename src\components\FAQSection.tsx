import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { MinimalCard } from "@/components/ui/minimal-card";

const FAQSection = () => {
  const faqs = [
    {
      question: "Is my data secure with InvestPro?",
      answer: "Absolutely! We use bank-grade security with 256-bit SSL encryption. Your data is stored on secure servers with regular backups. We are ISO 27001 certified and comply with all Indian data protection regulations."
    },
    {
      question: "Is InvestPro easy to use for someone who isn't tech-savvy?",
      answer: "Yes! InvestPro is designed with simplicity in mind. Our intuitive interface requires no technical knowledge. We also provide free training sessions and 24/7 support to help you get started quickly."
    },
    {
      question: "What kind of support do you offer?",
      answer: "We provide comprehensive support including 24/7 phone and email support, video tutorials, live chat assistance, and free training sessions. Our dedicated support team understands Post Office operations and can help you maximize the software's potential."
    },
    {
      question: "Can I use InvestPro on my mobile phone?",
      answer: "Yes! InvestPro works perfectly on all devices - mobile phones, tablets, and computers. Our responsive design ensures you can manage your business on the go with the same functionality as the desktop version."
    },
    {
      question: "How does the free trial work?",
      answer: "You get a complete 7-day free trial with access to all features. No credit card required to start. You can import your existing data and explore all functionalities. Our team will help you set up during the trial period."
    },
    {
      question: "Can I import my existing client data?",
      answer: "Yes! Our team will help you import all your existing client data from Excel files, other software, or manual records. The migration process is free and typically takes 1-2 business days depending on your data volume."
    },
    {
      question: "What happens if I want to cancel?",
      answer: "You can cancel anytime with no questions asked. There are no long-term contracts or cancellation fees. You can also export all your data before canceling to ensure you never lose your information."
    },
    {
      question: "Do you provide training for my team?",
      answer: "Yes! We provide comprehensive training for you and your team members. This includes live video sessions, detailed documentation, and ongoing support to ensure everyone can use the system effectively."
    }
  ];

  return (
    <section className="py-20 bg-background-secondary">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Frequently Asked{" "}
            <span className="bg-gradient-to-r from-[#113F67] to-[#40aca3] bg-clip-text text-transparent">
              Questions
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Got questions? We have answers. If you can't find what you're looking for, feel free to contact our support team.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <Accordion type="single" collapsible className="space-y-4">
            {faqs.map((faq, index) => (
              <MinimalCard key={index} className="group hover:scale-[1.01] hover:shadow-lg transition-all duration-300 bg-white border border-gray-100">
                <AccordionItem 
                  value={`item-${index}`}
                  className="border-none"
                >
                  <AccordionTrigger className="text-left font-semibold text-gray-900 hover:text-[#113F67] py-6 px-6 group-hover:text-[#113F67] transition-colors">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-gray-600 leading-relaxed pb-6 px-6">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              </MinimalCard>
            ))}
          </Accordion>
        </div>

        <div className="text-center mt-12">
          <MinimalCard className="inline-block bg-gradient-to-r from-[#113F67]/5 to-[#40aca3]/5 border border-gray-100 px-8 py-6">
            <p className="text-gray-600 mb-4">
              Still have questions?
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <a 
                href="mailto:<EMAIL>" 
                className="text-[#113F67] hover:text-[#40aca3] font-semibold transition-colors"
              >
                <EMAIL>
              </a>
              <span className="text-gray-400 hidden sm:block">•</span>
              <a 
                href="tel:+919876543210" 
                className="text-[#113F67] hover:text-[#40aca3] font-semibold transition-colors"
              >
                +91 98765 43210
              </a>
            </div>
          </MinimalCard>
        </div>
      </div>
    </section>
  );
};

export default FAQSection;