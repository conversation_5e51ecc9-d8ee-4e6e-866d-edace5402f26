import React from "react"
import { ArrowRight } from "lucide-react"
import { cn } from "@/lib/utils"

interface WrapButtonProps {
    className?: string
    children: React.ReactNode
    onClick?: () => void
}

const WrapButton: React.FC<WrapButtonProps> = ({
    className,
    children,
    onClick,
}) => {
    return (
        <div className="flex items-center justify-center">
            <div
                onClick={onClick}
                className={cn(
                    "group cursor-pointer border border-gray-300 bg-white hover:bg-gray-50 gap-2 h-12 flex items-center p-2 rounded-full shadow-sm hover:shadow-md transition-all",
                    className
                )}
            >
                <div className="bg-gradient-to-r from-[#113F67] to-[#40aca3] h-8 px-4 rounded-full flex items-center justify-center text-white">
                    <p className="font-medium text-sm">
                        {children}
                    </p>
                </div>
                <div className="text-gray-600 group-hover:ml-1 transition-all w-6 h-6 flex items-center justify-center rounded-full">
                    <ArrowRight
                        size={16}
                        className="group-hover:rotate-45 transition-all"
                    />
                </div>
            </div>
        </div>
    )
}

export { WrapButton }
