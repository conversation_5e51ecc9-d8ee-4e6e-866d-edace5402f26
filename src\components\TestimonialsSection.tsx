import { <PERSON>mal<PERSON>ard, MinimalCardContent, MinimalCardTitle, MinimalCardDescription } from "@/components/ui/minimal-card";
import { Star, Quote } from "lucide-react";

const TestimonialsSection = () => {
  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      location: "Bardoli, Gujarat",
      role: "Post Office Agent",
      rating: 5,
      content: "InvestPro has changed the way I work. I am free from paperwork now and can focus more on my clients. The automated alerts ensure I never miss any important dates.",
      avatar: "SS"
    },
    {
      name: "<PERSON><PERSON>",
      location: "Delhi",
      role: "Senior Post Office Agent",
      rating: 5,
      content: "The automated maturity alert feature is brilliant! No more missed deadlines. My clients are impressed with the professional reports I can generate instantly.",
      avatar: "R<PERSON>"
    },
    {
      name: "<PERSON><PERSON>",
      location: "Mumbai, Maharashtra",
      role: "Post Office Agent",
      rating: 5,
      content: "Managing 300+ clients was a nightmare before InvestPro. Now everything is organized and I can track all investments, commissions, and client data in one place.",
      avatar: "PP"
    }
  ];

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            What Our{" "}
            <span className="bg-gradient-to-r from-[#113F67] to-[#40aca3] bg-clip-text text-transparent">
              Agents Say
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Join thousands of successful Post Office agents who have transformed their business with InvestPro.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {testimonials.map((testimonial, index) => (
            <MinimalCard key={index} className="group hover:scale-[1.02] hover:shadow-xl transition-all duration-500 bg-white border border-gray-100">
              <MinimalCardContent className="p-8 space-y-6">
                <div className="flex items-center justify-between">
                  <div className="flex gap-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 fill-current text-yellow-400" />
                    ))}
                  </div>
                  <Quote className="w-8 h-8 text-[#113F67]/20 group-hover:text-[#113F67]/30 transition-colors" />
                </div>

                <MinimalCardDescription className="text-gray-600 leading-relaxed text-base italic">
                  "{testimonial.content}"
                </MinimalCardDescription>

                <div className="flex items-center gap-4 pt-4 border-t border-gray-100">
                  <div className="w-12 h-12 bg-gradient-to-br from-[#113F67] to-[#40aca3] text-white rounded-full flex items-center justify-center font-semibold shadow-lg">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <MinimalCardTitle className="font-semibold text-gray-900 text-base">{testimonial.name}</MinimalCardTitle>
                    <p className="text-sm text-gray-600">{testimonial.role}</p>
                    <p className="text-sm text-[#113F67] font-medium">{testimonial.location}</p>
                  </div>
                </div>
              </MinimalCardContent>
            </MinimalCard>
          ))}
        </div>

        <div className="text-center mt-12">
          <MinimalCard className="inline-flex items-center gap-4 bg-gradient-to-r from-[#113F67]/5 to-[#40aca3]/5 border border-gray-100 px-8 py-4">
            <div className="flex gap-1">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-5 h-5 fill-current text-yellow-400" />
              ))}
            </div>
            <span className="text-gray-900 font-semibold">4.9/5 rating</span>
            <span className="text-gray-600">from 1000+ agents</span>
          </MinimalCard>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;